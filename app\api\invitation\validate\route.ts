import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

interface JWTInvitationData {
  masterEmail: string;
  childEmail: string;
  masterId: string;
  type: string;
}

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { valid: false, error: 'Token is required' },
        { status: 400 }
      );
    }

    console.log('Validating JWT token:', token.substring(0, 20) + '...');
    console.log('JWT_SECRET available:', !!process.env.JWT_SECRET);

    // Validate JWT token
    try {
      const JWT_SECRET = process.env.JWT_SECRET;
      if (!JWT_SECRET) {
        console.error('JWT_SECRET environment variable is not set');
        return NextResponse.json({
          valid: false,
          error: 'Server configuration error'
        });
      }

      const decoded = jwt.verify(token, JWT_SECRET) as JWTInvitationData;

      if (!decoded || decoded.type !== 'invitation') {
        return NextResponse.json({
          valid: false,
          error: 'Invalid token format'
        });
      }

      return NextResponse.json({
        valid: true,
        invitationData: {
          masterEmail: decoded.masterEmail,
          childEmail: decoded.childEmail,
          masterId: decoded.masterId,
          type: decoded.type
        }
      });
    } catch (jwtError) {
      console.error('JWT token validation error:', jwtError);
      console.error('JWT_SECRET length:', process.env.JWT_SECRET?.length || 0);
      return NextResponse.json({
        valid: false,
        error: 'Invalid or expired invitation token'
      });
    }

  } catch (error) {
    console.error('Token validation error:', error);
    return NextResponse.json(
      { valid: false, error: 'Failed to validate token' },
      { status: 500 }
    );
  }
}
