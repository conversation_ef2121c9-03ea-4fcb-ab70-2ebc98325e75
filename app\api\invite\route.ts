import { NextRequest, NextResponse } from 'next/server';
import { sendEmail } from '@/utils/email';
import jwt from 'jsonwebtoken';
import { isDemoMode } from '../../config/demoMode';
import { getAppUrlFromRequest } from '../../../lib/utils/url';

// HTML email template function
function generateEmailTemplate(masterEmail: string, childEmail: string, zerodhaAuthUrl: string, isDemo: boolean = false) {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CopyTrade Invitation</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fa;
            margin: 0;
            padding: 0;
          }

          .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
            margin-bottom: 20px;
          }

          .header {
            background: linear-gradient(135deg, #387ed1 0%, #2563eb 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
          }

          .header h1 {
            margin: 0;
            font-weight: 600;
            font-size: 28px;
            letter-spacing: 0.5px;
          }

          .logo {
            margin-bottom: 15px;
            font-size: 32px;
            font-weight: 700;
          }

          .content {
            padding: 30px;
            background-color: #ffffff;
          }

          .message-box {
            background-color: #f0f7ff;
            border-left: 4px solid #387ed1;
            padding: 15px;
            margin-bottom: 25px;
            border-radius: 0 4px 4px 0;
          }

          .button-container {
            text-align: center;
            margin: 25px 0;
          }

          .button {
            background: linear-gradient(to right, #387ed1, #2563eb);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 500;
            display: inline-block;
            text-align: center;
            min-width: 200px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(56, 126, 209, 0.3);
          }

          .info-section {
            margin: 25px 0;
            padding: 20px;
            background-color: #f9fafb;
            border-radius: 8px;
          }

          .info-section h3 {
            margin-top: 0;
            color: #387ed1;
            font-weight: 600;
          }

          .divider {
            height: 1px;
            background-color: #e5e7eb;
            margin: 25px 0;
          }

          .footer {
            padding: 20px;
            text-align: center;
            font-size: 13px;
            color: #6b7280;
            background-color: #f9fafb;
            border-top: 1px solid #e5e7eb;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">📈 CopyTrade</div>
            <h1>You've Been Invited!</h1>
          </div>
          <div class="content">
            <div class="message-box">
              <p>Hello ${childEmail},</p>
              <p>You have been invited by <strong>${masterEmail}</strong> to join CopyTrade as a Child account.</p>
            </div>

            <div class="info-section">
              <h3>What is a Child Account?</h3>
              <p>As a Child account, all trades made by <strong>${masterEmail}</strong> will be automatically copied to your ${isDemo ? 'simulated' : 'trading'} account, allowing you to benefit from their trading expertise without manual intervention.</p>
            </div>

            ${isDemo ? `
            <div class="info-section" style="background: #e3f2fd; border: 2px solid #2196f3; border-radius: 8px; padding: 16px; margin: 16px 0;">
              <h3 style="color: #1976d2; margin-top: 0;">🚀 Demo Mode Active</h3>
              <p style="color: #1976d2; margin-bottom: 8px;"><strong>This is a safe demo environment!</strong></p>
              <ul style="color: #1976d2; margin: 8px 0; padding-left: 20px;">
                <li>No real money is involved</li>
                <li>All trades are simulated</li>
                <li>Experience the full CopyTrade workflow safely</li>
                <li>Perfect for testing and learning</li>
              </ul>
            </div>
            ` : ''}

            <div class="button-container">
              <a href="${zerodhaAuthUrl}" class="button">${isDemo ? 'Accept & Connect' : 'Accept & Connect to Broker'}</a>
            </div>

            <div class="divider"></div>

            <p><strong>How it works:</strong></p>
            <ol>
              <li>Click the button above to accept the invitation and connect your ${isDemo ? 'demo' : 'trading'} account</li>
              <li>${isDemo ? 'Join the demo environment instantly' : 'Authorize CopyTrade to access your trading account'}</li>
              <li>Start receiving trade copies automatically</li>
            </ol>

            <p><small>This invitation link will expire in 7 days. For security reasons, please do not share this email.</small></p>
          </div>
          <div class="footer">
            <p>If you did not request this invitation, please ignore this email.</p>
            <p>&copy; ${new Date().getFullYear()} CopyTrade. All rights reserved.</p>
          </div>
        </div>
      </body>
    </html>
  `;
}

// Send invitation handler
export async function POST(request: NextRequest) {
  try {
    const { masterEmail, childEmail, masterId } = await request.json();

    // Validate input
    if (!masterEmail || !childEmail || !masterId) {
      return NextResponse.json(
        { error: 'Master email, child email, and master ID are required' },
        { status: 400 }
      );
    }

    // Create invitation token
    const JWT_SECRET = process.env.JWT_SECRET;
    if (!JWT_SECRET) {
      return NextResponse.json(
        { error: 'Server configuration error: JWT secret not configured' },
        { status: 500 }
      );
    }

    const token = jwt.sign(
      {
        masterEmail,
        childEmail,
        masterId,
        type: 'invitation',
      },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    // Get API key from environment variable
    const apiKey = process.env.NEXT_PUBLIC_ZERODHA_API_KEY;

    // Create redirect URL with token
    const appUrl = getAppUrlFromRequest(request);
    const redirectUrl = `${appUrl}/auth/accept-invitation?token=${token}`;

    // For demo mode, use simple demo connection URL
    // For production mode, use Zerodha auth URL
    let zerodhaAuthUrl;
    if (isDemoMode()) {
      // Demo mode: direct connection without Zerodha OAuth
      zerodhaAuthUrl = `${appUrl}/auth/demo-connect?token=${token}`;
    } else {
      // Production mode: Zerodha OAuth flow
      const redirectParams = encodeURIComponent(JSON.stringify({
        invitation_token: token,
        master_id: masterId,
        child_email: childEmail
      }));

      zerodhaAuthUrl = apiKey
        ? `https://kite.zerodha.com/connect/login?v=3&api_key=${apiKey}&redirect_params=${redirectParams}`
        : redirectUrl;
    }

    // Generate email HTML
    const html = generateEmailTemplate(masterEmail, childEmail, zerodhaAuthUrl, isDemoMode());

    try {
      // Send the email using Nodemailer
      const emailResult = await sendEmail({
        to: childEmail,
        subject: 'Invitation to join CopyTrade as a Child account',
        html: html
      });

      console.log('Email sent successfully to:', childEmail);

      // Return success response with email details
      return NextResponse.json({
        success: true,
        message: 'Invitation email sent successfully',
        messageId: emailResult.messageId,
        token: token // Include token in response for debugging if needed
      });
    } catch (error) {
      console.error('Error sending email via Nodemailer:', error);

      // Return error response
      return NextResponse.json(
        { error: 'Failed to send invitation email', details: error instanceof Error ? error.message : 'Unknown error' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error processing invitation request:', error);
    return NextResponse.json(
      { error: 'Failed to process invitation request', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
