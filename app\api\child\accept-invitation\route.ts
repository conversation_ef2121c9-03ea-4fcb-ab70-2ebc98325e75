import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { isDemoMode } from '@/app/config/demoMode';
import { UserRole } from '@prisma/client';

interface InvitationData {
  masterEmail: string;
  childEmail: string;
  masterId: string;
  type: string;
}

export async function POST(request: NextRequest) {
  try {
    const { token, childUserData } = await request.json();

    if (!token || !childUserData) {
      return NextResponse.json(
        { error: 'Token and child user data are required' },
        { status: 400 }
      );
    }

    // Validate the invitation token using the validation API
    let invitationData: InvitationData;

    try {
      // Use relative URL for internal API calls to avoid CORS issues
      const baseUrl = process.env.VERCEL_URL
        ? `https://${process.env.VERCEL_URL}`
        : process.env.NEXTAUTH_URL || 'http://localhost:3000';

      const validateResponse = await fetch(`${baseUrl}/api/invitation/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      if (!validateResponse.ok) {
        throw new Error('Token validation failed');
      }

      const validationResult = await validateResponse.json();

      if (!validationResult.valid) {
        return NextResponse.json(
          { error: validationResult.error || 'Invalid invitation token' },
          { status: 400 }
        );
      }

      invitationData = validationResult.invitationData;
    } catch (err) {
      return NextResponse.json(
        { error: 'Invalid or expired invitation token '+ JSON.stringify(err) },
        { status: 400 }
      );
    }

    // Validate that the child email matches the invitation
    if (invitationData.childEmail !== childUserData.email) {
      return NextResponse.json(
        { error: 'Child email does not match invitation' },
        { status: 400 }
      );
    }

    const demoMode = isDemoMode();

    try {
      // Check if master user exists
      const masterUser = await prisma.user.findUnique({
        where: { id: invitationData.masterId },
        include: { zerodhaCredentials: true }
      });

      if (!masterUser) {
        return NextResponse.json(
          { error: 'Master user not found' },
          { status: 404 }
        );
      }

      // Check if child user already exists
      let childUser;
      const existingChild = await prisma.user.findUnique({
        where: { email: childUserData.email },
        include: { zerodhaCredentials: true }
      });

      if (existingChild) {
        // Update existing child user and their Zerodha credentials
        childUser = await prisma.user.update({
          where: { email: childUserData.email },
          data: {
            name: childUserData.name,
            role: UserRole.CHILD,
            zerodhaCredentials: {
              upsert: {
                create: {
                  zerodhaUserId: childUserData.zerodhaUserId,
                  accessToken: childUserData.zerodhaAccessToken,
                  refreshToken: childUserData.zerodhaRefreshToken,
                  isConnected: true,
                },
                update: {
                  zerodhaUserId: childUserData.zerodhaUserId,
                  accessToken: childUserData.zerodhaAccessToken,
                  refreshToken: childUserData.zerodhaRefreshToken,
                  isConnected: true,
                }
              }
            }
          },
          include: { zerodhaCredentials: true }
        });
      } else {
        // Create new child user with Zerodha credentials
        childUser = await prisma.user.create({
          data: {
            email: childUserData.email,
            name: childUserData.name,
            role: UserRole.CHILD,
            zerodhaCredentials: {
              create: {
                zerodhaUserId: childUserData.zerodhaUserId,
                accessToken: childUserData.zerodhaAccessToken,
                refreshToken: childUserData.zerodhaRefreshToken,
                isConnected: true,
              }
            }
          },
          include: { zerodhaCredentials: true }
        });
      }

      // Create or update master-child relationship
      const existingRelation = await prisma.masterChildRelationship.findUnique({
        where: {
          masterId_childId: {
            masterId: invitationData.masterId,
            childId: childUser.id
          }
        }
      });

      if (existingRelation) {
        // Update existing relationship
        await prisma.masterChildRelationship.update({
          where: {
            masterId_childId: {
              masterId: invitationData.masterId,
              childId: childUser.id
            }
          },
          data: {
            isActive: true,
            connectedAt: new Date(),
          }
        });
      } else {
        // Create new relationship
        await prisma.masterChildRelationship.create({
          data: {
            masterId: invitationData.masterId,
            childId: childUser.id,
            isActive: true,
            connectedAt: new Date(),
          }
        });
      }

      // JWT tokens are stateless, no need to update invitation status in database

      console.log(`[${demoMode ? 'DEMO' : 'PROD'} MODE] Child user ${childUserData.email} successfully connected to master ${invitationData.masterEmail}`);

      return NextResponse.json({
        success: true,
        message: `Successfully connected child user to master${demoMode ? ' (Demo Mode)' : ''}`,
        childUser: {
          id: childUser.id,
          email: childUser.email,
          name: childUser.name,
          role: childUser.role
        },
        masterUser: {
          id: masterUser.id,
          email: masterUser.email,
          name: masterUser.name
        },
        demoMode
      });

    } catch (dbError) {
      console.error('Database error:', dbError);
      return NextResponse.json(
        { error: 'Database operation failed' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error accepting invitation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
