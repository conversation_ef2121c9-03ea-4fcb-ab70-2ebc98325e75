// Test script to verify invitation token flow
// Run with: node test-invitation-flow.js

const jwt = require('jsonwebtoken');

// Test JWT token creation and validation
function testJWTFlow() {
  console.log('🧪 Testing JWT Token Flow...\n');

  // Use the same secret as in your .env.local
  const JWT_SECRET = 'your-secret-key-for-jwt-tokens';
  
  // Create a test token (same as in your invite API)
  const testPayload = {
    masterEmail: '<EMAIL>',
    childEmail: '<EMAIL>',
    masterId: 'test-master-id',
    type: 'invitation',
  };

  console.log('1. Creating JWT token...');
  const token = jwt.sign(testPayload, JWT_SECRET, { expiresIn: '7d' });
  console.log('✅ Token created:', token.substring(0, 50) + '...\n');

  // Test token validation (same as in your validate API)
  console.log('2. Validating JWT token...');
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    console.log('✅ Token validation successful!');
    console.log('📄 Decoded payload:', decoded);
    console.log('🕒 Expires at:', new Date(decoded.exp * 1000).toISOString());
  } catch (error) {
    console.log('❌ Token validation failed:', error.message);
  }

  console.log('\n3. Testing with wrong secret...');
  try {
    const decoded = jwt.verify(token, 'wrong-secret');
    console.log('❌ This should not happen!');
  } catch (error) {
    console.log('✅ Correctly rejected with wrong secret:', error.message);
  }

  console.log('\n4. Testing with default fallback secret...');
  try {
    const decoded = jwt.verify(token, 'your-secret-key');
    console.log('❌ Token validated with fallback secret - this is the problem!');
  } catch (error) {
    console.log('✅ Correctly rejected with fallback secret:', error.message);
  }

  return token;
}

// Test API endpoint simulation
async function testAPIEndpoint(token) {
  console.log('\n🌐 Testing API Endpoint Simulation...\n');

  // Simulate the validation API endpoint
  function simulateValidationAPI(token, jwtSecret) {
    try {
      if (!jwtSecret) {
        return { valid: false, error: 'Server configuration error' };
      }

      const decoded = jwt.verify(token, jwtSecret);
      
      if (!decoded || decoded.type !== 'invitation') {
        return { valid: false, error: 'Invalid token format' };
      }

      return {
        valid: true,
        invitationData: {
          masterEmail: decoded.masterEmail,
          childEmail: decoded.childEmail,
          masterId: decoded.masterId,
          type: decoded.type
        },
        tokenType: 'jwt'
      };
    } catch (error) {
      return { valid: false, error: 'Invalid or expired invitation token' };
    }
  }

  // Test with correct secret
  console.log('1. Testing with correct JWT_SECRET...');
  const result1 = simulateValidationAPI(token, 'your-secret-key-for-jwt-tokens');
  console.log('Result:', result1.valid ? '✅ Valid' : '❌ Invalid');
  if (!result1.valid) console.log('Error:', result1.error);

  // Test with missing secret (Vercel issue)
  console.log('\n2. Testing with missing JWT_SECRET (Vercel issue)...');
  const result2 = simulateValidationAPI(token, undefined);
  console.log('Result:', result2.valid ? '✅ Valid' : '❌ Invalid');
  if (!result2.valid) console.log('Error:', result2.error);

  // Test with fallback secret
  console.log('\n3. Testing with fallback secret...');
  const result3 = simulateValidationAPI(token, 'your-secret-key');
  console.log('Result:', result3.valid ? '✅ Valid' : '❌ Invalid');
  if (!result3.valid) console.log('Error:', result3.error);
}

// Run tests
console.log('🔧 CopyTrade Invitation Token Test\n');
console.log('This script tests the JWT token flow to identify the Vercel deployment issue.\n');

const token = testJWTFlow();
testAPIEndpoint(token);

console.log('\n📋 Summary:');
console.log('- The issue occurs when JWT_SECRET is not set in Vercel environment variables');
console.log('- Tokens created locally with one secret cannot be validated with a different secret');
console.log('- Make sure to set JWT_SECRET=your-secret-key-for-jwt-tokens in Vercel');
console.log('- After setting environment variables, redeploy your Vercel application');
